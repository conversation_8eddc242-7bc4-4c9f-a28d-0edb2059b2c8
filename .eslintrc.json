{
	"extends": [
		"next/core-web-vitals",
		"@typescript-eslint/recommended",
		"@typescript-eslint/recommended-requiring-type-checking"
	],
	"parser": "@typescript-eslint/parser",
	"parserOptions": {
		"ecmaVersion": "latest",
		"sourceType": "module",
		"project": "./tsconfig.json"
	},
	"plugins": ["@typescript-eslint"],
	"rules": {
		// TypeScript-specific rules for type safety
		"@typescript-eslint/no-explicit-any": "error",
		"@typescript-eslint/no-unsafe-any": "error",
		"@typescript-eslint/no-unsafe-assignment": "error",
		"@typescript-eslint/no-unsafe-call": "error",
		"@typescript-eslint/no-unsafe-member-access": "error",
		"@typescript-eslint/no-unsafe-return": "error",
		"@typescript-eslint/no-unsafe-argument": "error",
		"@typescript-eslint/strict-boolean-expressions": "error",
		"@typescript-eslint/prefer-nullish-coalescing": "error",
		"@typescript-eslint/prefer-optional-chain": "error",
		"@typescript-eslint/no-non-null-assertion": "error",
		"@typescript-eslint/no-unnecessary-type-assertion": "error",
		"@typescript-eslint/no-unused-vars": [
			"error",
			{
				"argsIgnorePattern": "^_",
				"varsIgnorePattern": "^_"
			}
		],
		"@typescript-eslint/explicit-function-return-type": [
			"error",
			{
				"allowExpressions": true,
				"allowTypedFunctionExpressions": true,
				"allowHigherOrderFunctions": true,
				"allowDirectConstAssertionInArrowFunctions": true
			}
		],
		"@typescript-eslint/consistent-type-imports": [
			"error",
			{
				"prefer": "type-imports"
			}
		],
		"@typescript-eslint/consistent-type-definitions": ["error", "interface"],
		"@typescript-eslint/array-type": ["error", { "default": "array" }],
		"@typescript-eslint/prefer-readonly": "error",
		"@typescript-eslint/prefer-readonly-parameter-types": "off",
		"@typescript-eslint/switch-exhaustiveness-check": "error",
		
		// General code quality rules
		"prefer-const": "error",
		"no-var": "error",
		"no-console": ["warn", { "allow": ["warn", "error"] }],
		"eqeqeq": ["error", "always"],
		"curly": ["error", "all"]
	},
	"overrides": [
		{
			"files": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"],
			"rules": {
				"@typescript-eslint/no-explicit-any": "off",
				"@typescript-eslint/no-unsafe-any": "off",
				"@typescript-eslint/no-unsafe-assignment": "off",
				"@typescript-eslint/no-unsafe-call": "off",
				"@typescript-eslint/no-unsafe-member-access": "off",
				"@typescript-eslint/no-unsafe-return": "off",
				"@typescript-eslint/explicit-function-return-type": "off"
			}
		}
	]
}
